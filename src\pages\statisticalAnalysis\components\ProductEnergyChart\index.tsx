import * as React from 'react';
import { Table, Modal, Select } from 'antd';
import * as echarts from 'echarts/core';
import { BarChart, LineChart } from 'echarts/charts';
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import style from './index.module.less';
import { ECBasicOption } from 'echarts/types/dist/shared';

// 注册ECharts组件
echarts.use([
  BarChart,
  LineChart,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  CanvasRenderer,
]);

/**
 * 单品能耗图表组件属性接口
 */
interface ProductEnergyChartProps {
  // 不需要额外的props
}

/**
 * 产品类型定义
 */
interface ProductType {
  value: string;
  label: string;
}

/**
 * 公司能耗数据定义
 */
interface CompanyEnergyData {
  key: string;
  company: string;
  energy: number;
  unit: string;
}

/**
 * 单品能耗图表组件
 *
 * 显示不同公司的单品能耗情况，使用柱状图+折线图展示
 * 柱状图显示各公司的单品能耗，折线图显示平均水平
 * 前面有产品选择框，默认选择乙烯
 * 点击"更多"按钮可查看详细的产品能耗对比数据
 *
 * @param props - 组件属性
 * @returns 单品能耗图表组件
 */
const ProductEnergyChart: React.FC<ProductEnergyChartProps> = () => {
  const chartRef = React.useRef<HTMLDivElement>(null);
  const chartInstance = React.useRef<echarts.ECharts | null>(null);
  const [isModalVisible, setIsModalVisible] = React.useState(false);
  const [selectedProduct, setSelectedProduct] = React.useState<string>('ethylene');

  // 产品类型选项
  const productOptions: ProductType[] = [
    { value: 'ethylene', label: '乙烯' },
    { value: 'propylene', label: '丙烯' },
    { value: 'benzene', label: '苯' },
    { value: 'toluene', label: '甲苯' },
    { value: 'xylene', label: '二甲苯' },
  ];

  // 模拟不同产品的公司能耗数据
  const productDataMap: Record<string, CompanyEnergyData[]> = {
    ethylene: [
      { key: '1', company: '公司A', energy: 0.85, unit: 'tce/吨' },
      { key: '2', company: '公司B', energy: 0.92, unit: 'tce/吨' },
      { key: '3', company: '公司C', energy: 0.78, unit: 'tce/吨' },
      { key: '4', company: '公司D', energy: 0.88, unit: 'tce/吨' },
      { key: '5', company: '公司E', energy: 0.95, unit: 'tce/吨' },
      { key: '6', company: '公司F', energy: 0.82, unit: 'tce/吨' },
    ],
    propylene: [
      { key: '1', company: '公司A', energy: 0.72, unit: 'tce/吨' },
      { key: '2', company: '公司B', energy: 0.68, unit: 'tce/吨' },
      { key: '3', company: '公司C', energy: 0.75, unit: 'tce/吨' },
      { key: '4', company: '公司D', energy: 0.71, unit: 'tce/吨' },
      { key: '5', company: '公司E', energy: 0.79, unit: 'tce/吨' },
      { key: '6', company: '公司F', energy: 0.73, unit: 'tce/吨' },
    ],
    benzene: [
      { key: '1', company: '公司A', energy: 0.45, unit: 'tce/吨' },
      { key: '2', company: '公司B', energy: 0.52, unit: 'tce/吨' },
      { key: '3', company: '公司C', energy: 0.48, unit: 'tce/吨' },
      { key: '4', company: '公司D', energy: 0.51, unit: 'tce/吨' },
      { key: '5', company: '公司E', energy: 0.47, unit: 'tce/吨' },
      { key: '6', company: '公司F', energy: 0.49, unit: 'tce/吨' },
    ],
    toluene: [
      { key: '1', company: '公司A', energy: 0.38, unit: 'tce/吨' },
      { key: '2', company: '公司B', energy: 0.42, unit: 'tce/吨' },
      { key: '3', company: '公司C', energy: 0.35, unit: 'tce/吨' },
      { key: '4', company: '公司D', energy: 0.41, unit: 'tce/吨' },
      { key: '5', company: '公司E', energy: 0.39, unit: 'tce/吨' },
      { key: '6', company: '公司F', energy: 0.37, unit: 'tce/吨' },
    ],
    xylene: [
      { key: '1', company: '公司A', energy: 0.55, unit: 'tce/吨' },
      { key: '2', company: '公司B', energy: 0.61, unit: 'tce/吨' },
      { key: '3', company: '公司C', energy: 0.58, unit: 'tce/吨' },
      { key: '4', company: '公司D', energy: 0.59, unit: 'tce/吨' },
      { key: '5', company: '公司E', energy: 0.63, unit: 'tce/吨' },
      { key: '6', company: '公司F', energy: 0.56, unit: 'tce/吨' },
    ],
  };

  // 获取当前选中产品的数据
  const currentData = productDataMap[selectedProduct] || productDataMap.ethylene;

  // 计算平均值
  const averageEnergy = React.useMemo(() => {
    const total = currentData.reduce((sum, item) => sum + item.energy, 0);
    return total / currentData.length;
  }, [currentData]);

  // 获取当前选中产品的中文名称
  const currentProductLabel =
    productOptions.find((option) => option.value === selectedProduct)?.label || '乙烯';

  // ECharts配置选项
  const chartOption: ECBasicOption = React.useMemo(
    () => ({
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999',
          },
        },
        formatter: (params: any) => {
          let result = `${params[0].name}<br/>`;
          params.forEach((item: any) => {
            if (item.seriesType === 'bar') {
              result += `${item.seriesName}: ${item.value} ${currentData[0]?.unit || 'tce/吨'}<br/>`;
            } else if (item.seriesType === 'line') {
              result += `${item.seriesName}: ${item.value} ${currentData[0]?.unit || 'tce/吨'}<br/>`;
            }
          });
          return result;
        },
      },
      legend: {
        data: [`${currentProductLabel}单品能耗`, '平均水平'],
        top: 10,
        textStyle: {
          color: '#8c8c8c',
          fontSize: 12,
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: currentData.map((item) => item.company),
          axisPointer: {
            type: 'shadow',
          },
          axisLabel: {
            color: '#8c8c8c',
            fontSize: 12,
            rotate: 45, // 斜着显示45度
            interval: 0, // 强制显示所有标签
          },
          axisLine: {
            lineStyle: {
              color: '#f0f0f0',
            },
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          name: currentData[0]?.unit || 'tce/吨',
          nameTextStyle: {
            color: '#8c8c8c',
            fontSize: 12,
          },
          axisLabel: {
            color: '#8c8c8c',
            fontSize: 12,
            formatter: '{value}',
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: '#f5f5f5',
              type: 'dashed',
            },
          },
        },
      ],
      series: [
        {
          name: `${currentProductLabel}单品能耗`,
          type: 'bar',
          data: currentData.map((item) => item.energy),
          itemStyle: {
            color: '#1890ff', // 统一使用蓝色
          },
          barWidth: '60%',
        },
        {
          name: '平均水平',
          type: 'line',
          data: currentData.map(() => averageEnergy),
          lineStyle: {
            color: '#ff7875', // 使用橙红色区分
            width: 2,
            type: 'dashed',
          },
          itemStyle: {
            color: '#ff7875',
          },
          symbol: 'none', // 不显示数据点
        },
      ],
    }),
    [currentData, averageEnergy, currentProductLabel],
  );

  // 初始化图表
  React.useEffect(() => {
    if (chartRef.current) {
      chartInstance.current = echarts.init(chartRef.current);
      chartInstance.current.setOption(chartOption);
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, []);

  // 更新图表配置
  React.useEffect(() => {
    if (chartInstance.current) {
      chartInstance.current.setOption(chartOption);
    }
  }, [chartOption]);

  // 处理窗口大小变化
  React.useEffect(() => {
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  /**
   * 处理产品选择变化
   */
  const handleProductChange: (value: string) => void = (value: string) => {
    setSelectedProduct(value);
  };

  /**
   * 处理"更多"按钮点击事件
   */
  const handleMoreClick: () => void = () => {
    setIsModalVisible(true);
  };

  /**
   * 处理Modal关闭事件
   */
  const handleModalClose: () => void = () => {
    setIsModalVisible(false);
  };

  // 详细数据表格列配置
  const columns = [
    {
      title: '公司名称',
      dataIndex: 'company',
      key: 'company',
    },
    {
      title: `${currentProductLabel}单品能耗`,
      dataIndex: 'energy',
      key: 'energy',
      render: (value: number) => value.toFixed(3), // 保留3位小数
    },
    {
      title: '平均水平',
      key: 'average',
      render: () => averageEnergy.toFixed(3), // 保留3位小数
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
    },
    {
      title: '与平均值对比',
      key: 'comparison',
      render: (record: CompanyEnergyData) => {
        // 计算与平均值的差异百分比
        const diff = ((record.energy - averageEnergy) / averageEnergy) * 100;
        const isHigher = diff > 0;
        return (
          <span style={{ color: isHigher ? '#ff4d4f' : '#52c41a' }}>
            {isHigher ? '+' : ''}
            {diff.toFixed(1)}%
          </span>
        );
      },
    },
  ];

  const modalContent = (
    <div>
      <p style={{ marginBottom: 16, color: '#595959' }}>
        以下是各公司{currentProductLabel}单品能耗的详细数据，包含与平均值的对比：
      </p>
      <Table columns={columns} dataSource={currentData} pagination={false} size="small" bordered />
    </div>
  );

  return (
    <>
      <div className={style['product-energy-chart']}>
        {/* 图表标题栏 */}
        <div className={style['chart-header']}>
          <div className={style['chart-title']}>单品能耗</div>
          <div className={style['chart-controls']}>
            <Select
              value={selectedProduct}
              onChange={handleProductChange}
              options={productOptions}
              className={style['product-selector']}
              size="small"
            />
            <div
              className={style['chart-more']}
              onClick={handleMoreClick}
              onKeyDown={(e) => e.key === 'Enter' && handleMoreClick()}
              role="button"
              tabIndex={0}
            >
              更多
            </div>
          </div>
        </div>

        {/* ECharts图表容器 */}
        <div ref={chartRef} className={style['chart-container']} />
      </div>

      {/* 详细数据Modal */}
      <Modal
        title={`${currentProductLabel}单品能耗 - 详细数据`}
        visible={isModalVisible}
        onCancel={handleModalClose}
        footer={null}
        width={800}
        className={style['chart-modal']}
      >
        {modalContent}
      </Modal>
    </>
  );
};

export default ProductEnergyChart;
