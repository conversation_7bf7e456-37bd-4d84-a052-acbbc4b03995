.product-energy-chart {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1; // 自适应父容器

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f5f5f5;

    .chart-title {
      font-size: 14px;
      font-weight: 600;
      color: #262626;
      line-height: 1.4;
    }

    .chart-controls {
      display: flex;
      align-items: center;
      gap: 12px;

      .product-selector {
        min-width: 80px;

        :global(.ant-select-selector) {
          border-color: #d9d9d9;
          border-radius: 4px;

          &:hover {
            border-color: #40a9ff;
          }
        }

        :global(.ant-select-focused) {
          :global(.ant-select-selector) {
            border-color: #40a9ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }
      }

      .chart-more {
        font-size: 12px;
        color: #1890ff;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          background: #f0f8ff;
          color: #096dd9;
        }

        &:active {
          background: #e6f7ff;
        }
      }
    }
  }

  .chart-container {
    flex: 1;
    min-height: 200px;
    width: 100%;
  }
}

/* Modal样式 */
.chart-modal {
  .modal-placeholder {
    text-align: center;
    padding: 40px 20px;
    color: #8c8c8c;

    p {
      margin-bottom: 8px;
      line-height: 1.6;

      &:first-child {
        font-size: 16px;
        font-weight: 500;
        color: #595959;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .product-energy-chart {
    padding: 12px;

    .chart-header {
      margin-bottom: 12px;
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;

      .chart-title {
        font-size: 13px;
      }

      .chart-controls {
        align-self: flex-end;

        .product-selector {
          min-width: 70px;
        }

        .chart-more {
          font-size: 11px;
          padding: 3px 6px;
        }
      }
    }

    .chart-container {
      min-height: 160px;
    }
  }
}
