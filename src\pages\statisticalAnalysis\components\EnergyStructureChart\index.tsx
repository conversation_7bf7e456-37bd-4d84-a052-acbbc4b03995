import * as React from 'react';
import { Table, Modal } from 'antd';
import * as echarts from 'echarts/core';
import { PieChart } from 'echarts/charts';
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { ECBasicOption } from 'echarts/types/dist/shared';
import style from './index.module.less';

// 注册ECharts组件
echarts.use([PieChart, GridComponent, TooltipComponent, LegendComponent, CanvasRenderer]);

/**
 * 能源结构图表组件属性接口
 */
interface EnergyStructureChartProps {
  // 不需要额外的props
}

/**
 * 能源数据定义
 */
interface EnergyData {
  key: string;
  energyType: string;
  amount: number;
  percentage: number;
  unit: string;
}

/**
 * 能源结构图表组件
 *
 * 显示企业整体能源消耗的结构分布，使用饼图展示
 * 可以直观看出各类能源在总能耗中的占比情况
 * 点击"更多"按钮可查看详细的能源结构数据表格
 *
 * @param props - 组件属性
 * @returns 能源结构图表组件
 */
const EnergyStructureChart: React.FC<EnergyStructureChartProps> = () => {
  const chartRef = React.useRef<HTMLDivElement>(null);
  const chartInstance = React.useRef<echarts.ECharts | null>(null);
  const [isModalVisible, setIsModalVisible] = React.useState(false);

  // 模拟详细数据 - 各类能源的消耗量和占比
  const detailData: EnergyData[] = [
    { key: '1', energyType: '煤炭', amount: 621.8, percentage: 44.9, unit: '万tce' },
    { key: '2', energyType: '电力', amount: 447.7, percentage: 32.3, unit: '万tce' },
    { key: '3', energyType: '天然气', amount: 216.4, percentage: 15.6, unit: '万tce' },
    { key: '4', energyType: '石油', amount: 69.2, percentage: 5.0, unit: '万tce' },
    { key: '5', energyType: '其他', amount: 28.9, percentage: 2.1, unit: '万tce' },
  ];

  // 能源类型颜色映射
  const energyColors: { [key: string]: string } = {
    煤炭: '#8c8c8c',
    电力: '#1890ff',
    天然气: '#52c41a',
    石油: '#fa8c16',
    其他: '#722ed1',
  };

  // ECharts饼图配置选项
  const chartOption: ECBasicOption = React.useMemo(
    () => ({
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          return `
            ${params.name}<br/>
            消耗量: ${params.data.amount} 万tce<br/>
            占比: ${params.percent}%
          `;
        },
      },
      legend: {
        orient: 'horizontal',
        left: 'center',
        bottom: '5%',
        textStyle: {
          color: '#8c8c8c',
          fontSize: 12,
        },
        itemWidth: 12,
        itemHeight: 12,
        itemGap: 16,
      },
      series: [
        {
          name: '能源结构',
          type: 'pie',
          radius: ['40%', '70%'], // 环形饼图
          center: ['50%', '45%'], // 居中显示，稍微上移为底部图例留空间
          avoidLabelOverlap: false,
          label: {
            show: true,
            position: 'outside',
            formatter: '{b}: {c}%',
            fontSize: 10,
            color: '#595959',
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 12,
              fontWeight: 'bold',
            },
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
          data: detailData.map((item) => ({
            name: item.energyType,
            value: item.percentage,
            amount: item.amount,
            itemStyle: {
              color: energyColors[item.energyType] || '#d9d9d9',
            },
          })),
        },
      ],
    }),
    [detailData, energyColors],
  );

  // 初始化图表
  React.useEffect(() => {
    if (chartRef.current) {
      chartInstance.current = echarts.init(chartRef.current);
      chartInstance.current.setOption(chartOption);
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, []);

  // 更新图表配置
  React.useEffect(() => {
    if (chartInstance.current) {
      chartInstance.current.setOption(chartOption);
    }
  }, [chartOption]);

  // 处理窗口大小变化
  React.useEffect(() => {
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  /**
   * 处理"更多"按钮点击事件
   */
  const handleMoreClick = () => {
    setIsModalVisible(true);
  };

  /**
   * 处理Modal关闭事件
   */
  const handleModalClose = () => {
    setIsModalVisible(false);
  };

  // 详细数据表格列配置
  const columns = [
    {
      title: '能源类型',
      dataIndex: 'energyType',
      key: 'energyType',
      render: (text: string) => {
        return (
          <span style={{ display: 'flex', alignItems: 'center' }}>
            <span
              style={{
                width: 12,
                height: 12,
                backgroundColor: energyColors[text] || '#d9d9d9',
                borderRadius: '50%',
                marginRight: 8,
              }}
            />
            {text}
          </span>
        );
      },
    },
    {
      title: '消耗量',
      dataIndex: 'amount',
      key: 'amount',
      render: (value: number, record: EnergyData) => `${value.toFixed(1)} ${record.unit}`,
    },
    {
      title: '占比',
      dataIndex: 'percentage',
      key: 'percentage',
      render: (value: number) => `${value.toFixed(1)}%`,
    },
  ];

  const modalContent = (
    <div>
      <p style={{ marginBottom: 16, color: '#595959' }}>
        以下是能源结构的详细数据，显示各类能源的消耗量和占比：
      </p>
      <Table columns={columns} dataSource={detailData} pagination={false} size="small" bordered />
      <div style={{ marginTop: 16, fontSize: 12, color: '#8c8c8c' }}>
        * 数据统计周期：当前选择的时间范围内的累计消耗量
      </div>
    </div>
  );

  return (
    <>
      <div className={style['energy-structure-chart']}>
        {/* 图表标题栏 */}
        <div className={style['chart-header']}>
          <div className={style['chart-title']}>能源结构</div>
          <div
            className={style['chart-more']}
            onClick={handleMoreClick}
            onKeyDown={(e) => e.key === 'Enter' && handleMoreClick()}
            role="button"
            tabIndex={0}
          >
            更多
          </div>
        </div>

        {/* ECharts图表容器 */}
        <div ref={chartRef} className={style['chart-container']} />
      </div>

      {/* 详细数据Modal */}
      <Modal
        title="能源结构 - 详细数据"
        visible={isModalVisible}
        onCancel={handleModalClose}
        footer={null}
        width={800}
        className={style['chart-modal']}
      >
        {modalContent}
      </Modal>
    </>
  );
};

export default EnergyStructureChart;
