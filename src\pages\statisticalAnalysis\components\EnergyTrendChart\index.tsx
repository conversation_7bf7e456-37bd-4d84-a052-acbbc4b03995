import * as React from 'react';
import { Table, Modal } from 'antd';
import * as echarts from 'echarts/core';
import { LineChart } from 'echarts/charts';
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import style from './index.module.less';
import { ECBasicOption } from 'echarts/types/dist/shared';

// 注册ECharts组件
echarts.use([LineChart, GridComponent, TooltipComponent, LegendComponent, CanvasRenderer]);

/**
 * 能耗趋势图表组件属性接口
 */
interface EnergyTrendChartProps {
  /** 额外的CSS类名 */
  className?: string;
}

/**
 * 能耗趋势图表组件
 *
 * 使用ECharts折线图显示企业能耗随时间变化的趋势
 * 点击"更多"按钮可查看详细的月度能耗数据表格
 *
 * @param props - 组件属性
 * @returns 能耗趋势图表组件
 */
const EnergyTrendChart: React.FC<EnergyTrendChartProps> = ({ className }) => {
  const chartRef = React.useRef<HTMLDivElement>(null);
  const chartInstance = React.useRef<echarts.ECharts | null>(null);
  const [isModalVisible, setIsModalVisible] = React.useState(false);

  // 模拟详细数据 - 各月份能耗量
  const detailData = [
    { key: '1', month: '2024-01', currentEnergy: 1420, lastYearEnergy: 1350, unit: '万tce' },
    { key: '2', month: '2024-02', currentEnergy: 1380, lastYearEnergy: 1320, unit: '万tce' },
    { key: '3', month: '2024-03', currentEnergy: 1450, lastYearEnergy: 1400, unit: '万tce' },
    { key: '4', month: '2024-04', currentEnergy: 1390, lastYearEnergy: 1360, unit: '万tce' },
    { key: '5', month: '2024-05', currentEnergy: 1410, lastYearEnergy: 1380, unit: '万tce' },
    { key: '6', month: '2024-06', currentEnergy: 1384, lastYearEnergy: 1340, unit: '万tce' },
  ];

  // ECharts配置选项
  const chartOption: ECBasicOption = React.useMemo(
    () => ({
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          let result = `${params[0].name}<br/>`;
          params.forEach((item: any) => {
            result += `${item.seriesName}: ${item.value} 万tce<br/>`;
          });
          return result;
        },
      },
      legend: {
        data: ['本期数据', '同期数据'],
        top: 10,
        textStyle: {
          color: '#8c8c8c',
          fontSize: 12,
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: detailData.map((item) => item.month.substring(5)), // 只显示月份
        axisLabel: {
          color: '#8c8c8c',
          fontSize: 12,
        },
        axisLine: {
          lineStyle: {
            color: '#f0f0f0',
          },
        },
      },
      yAxis: {
        type: 'value',
        name: '万tce',
        nameTextStyle: {
          color: '#8c8c8c',
          fontSize: 12,
        },
        axisLabel: {
          color: '#8c8c8c',
          fontSize: 12,
          formatter: '{value}',
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            color: '#f5f5f5',
            type: 'dashed',
          },
        },
      },
      series: [
        {
          name: '本期数据',
          type: 'line',
          smooth: true,
          data: detailData.map((item) => item.currentEnergy),
          lineStyle: {
            color: '#1890ff',
            width: 2,
            type: 'solid', // 实线
          },
          itemStyle: {
            color: '#1890ff',
          },
          symbol: 'circle',
          symbolSize: 6,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                { offset: 1, color: 'rgba(24, 144, 255, 0.05)' },
              ],
            },
          },
        },
        {
          name: '同期数据',
          type: 'line',
          smooth: true,
          data: detailData.map((item) => item.lastYearEnergy),
          lineStyle: {
            color: '#52c41a',
            width: 2,
            type: 'dashed', // 虚线
          },
          itemStyle: {
            color: '#52c41a',
          },
          symbol: 'circle',
          symbolSize: 6,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(82, 196, 26, 0.25)' },
                { offset: 1, color: 'rgba(82, 196, 26, 0.03)' },
              ],
            },
          },
        },
      ],
    }),
    [detailData],
  );

  // 初始化图表
  React.useEffect(() => {
    if (chartRef.current) {
      chartInstance.current = echarts.init(chartRef.current);
      chartInstance.current.setOption(chartOption);
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, []);

  // 更新图表配置
  React.useEffect(() => {
    if (chartInstance.current) {
      chartInstance.current.setOption(chartOption);
    }
  }, [chartOption]);

  // 处理窗口大小变化
  React.useEffect(() => {
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  /**
   * 处理"更多"按钮点击事件
   */
  const handleMoreClick = () => {
    setIsModalVisible(true);
  };

  /**
   * 处理Modal关闭事件
   */
  const handleModalClose = () => {
    setIsModalVisible(false);
  };

  // 详细数据表格列配置
  const columns = [
    {
      title: '月份',
      dataIndex: 'month',
      key: 'month',
    },
    {
      title: '本期数据',
      dataIndex: 'currentEnergy',
      key: 'currentEnergy',
      render: (value: number) => `${value.toLocaleString()}`, // 格式化数字显示
    },
    {
      title: '同期数据',
      dataIndex: 'lastYearEnergy',
      key: 'lastYearEnergy',
      render: (value: number) => `${value.toLocaleString()}`, // 格式化数字显示
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
    },
  ];

  const modalContent = (
    <div>
      <p style={{ marginBottom: 16, color: '#595959' }}>
        以下是能耗趋势的详细数据，包含各月份的具体能耗量：
      </p>
      <Table columns={columns} dataSource={detailData} pagination={false} size="small" bordered />
    </div>
  );

  return (
    <>
      <div className={`${style['energy-trend-chart']} ${className || ''}`}>
        {/* 图表标题栏 */}
        <div className={style['chart-header']}>
          <div className={style['chart-title']}>能耗趋势</div>
          <div
            className={style['chart-more']}
            onClick={handleMoreClick}
            onKeyDown={(e) => e.key === 'Enter' && handleMoreClick()}
            role="button"
            tabIndex={0}
          >
            更多
          </div>
        </div>

        {/* ECharts图表容器 */}
        <div ref={chartRef} className={style['chart-container']} />
      </div>

      {/* 详细数据Modal */}
      <Modal
        title="能耗趋势 - 详细数据"
        visible={isModalVisible}
        onCancel={handleModalClose}
        footer={null}
        width={800}
        className={style['chart-modal']}
      >
        {modalContent}
      </Modal>
    </>
  );
};

export default EnergyTrendChart;
